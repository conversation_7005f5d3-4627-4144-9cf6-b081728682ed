package com.xtc.marketing.xtcmarketing.rpc.scrm;

import com.xtc.marketing.xtcmarketing.exception.SysErrorCode;
import com.xtc.marketing.xtcmarketing.exception.SysException;
import com.xtc.marketing.xtcmarketing.rpc.scrm.request.BaseScrmRequest;
import com.xtc.marketing.xtcmarketing.rpc.scrm.request.GetSecondAgentRequest;
import com.xtc.marketing.xtcmarketing.rpc.scrm.response.BaseScrmResponse;
import com.xtc.marketing.xtcmarketing.rpc.scrm.response.GetSecondAgentResponse;
import com.xtc.marketing.xtcmarketing.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ScrmRpc
 */
@Slf4j
@Component
public class ScrmRpc {

    /**
     * 正式域名
     */
    private static final String DOMAIN = "https://scrm.okii.com";
    /**
     * 正式访问id
     */
    private static final String ACCESS_ID = "677489ad66c82f2b6780a9409958426a";
    /**
     * 正式访问密钥
     */
    private static final String ACCESS_SECRET = "b4307c35944984c2f857c3e3d23e58c5";
    /**
     * http 请求实例
     */
    private static final RestTemplate REST_TEMPLATE = new RestTemplate();

    static {
        // 设置字符串的转换使用UTF-8编码
        REST_TEMPLATE.getMessageConverters().stream()
                .filter(converter -> converter.getClass().equals(StringHttpMessageConverter.class))
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));
        // 设置请求超时时间
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(3000);
        requestFactory.setReadTimeout(5000);
        REST_TEMPLATE.setRequestFactory(requestFactory);
    }

    /**
     * 查询二级代理id列表
     *
     * @param userId 用户id
     * @return 二级代理id列表
     */
    public Set<String> listPublicSecondAgentIds(String userId) {
        // 获取二级代理列表
        List<GetSecondAgentResponse.SecondAgent> secondAgents = this.listSecondAgents(userId);
        // 提取二级代理id
        return secondAgents.stream()
                .map(GetSecondAgentResponse.SecondAgent::getPublicSecondAgentId)
                .collect(Collectors.toUnmodifiableSet());
    }

    /**
     * 获取二级代理列表
     *
     * @param userId 用户id
     * @return 二级代理列表
     */
    public List<GetSecondAgentResponse.SecondAgent> listSecondAgents(String userId) {
        // 构建请求
        GetSecondAgentRequest request = new GetSecondAgentRequest();
        request.setUserId(userId);
        // 调用接口
        GetSecondAgentResponse response = this.call(request);
        return response.getData() != null ? response.getData() : List.of();
    }

    /**
     * 调用接口
     *
     * @param request 请求
     * @param <T>     响应类型
     * @return 响应
     */
    private <T extends BaseScrmResponse<?>> T call(BaseScrmRequest<T> request) {
        // 初始化
        RequestEntity<?> requestEntity = this.createRequestEntity(request);
        String responseStr = "";
        String requestInfoLog = "SCRM RPC %s url: %s, header: %s, body: %s"
                .formatted(requestEntity.getMethod(), requestEntity.getUrl(), requestEntity.getHeaders(), requestEntity.getBody());
        try {
            // 发起请求
            log.info(requestInfoLog);
            ResponseEntity<String> responseEntity = REST_TEMPLATE.exchange(requestEntity, String.class);
            responseStr = responseEntity.getBody();
            log.info("{}, response: {}", requestInfoLog, responseStr);
            // 解析响应
            T response = GsonUtil.jsonToBean(responseStr, request.getResponseClass());
            if (response == null || response.failure()) {
                throw this.remoteException();
            }
            return response;
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            if (e instanceof HttpStatusCodeException) {
                responseStr = e.getMessage();
            }
            throw rpcSysException(responseStr, requestEntity, e);
        }
    }

    /**
     * 生成请求
     *
     * @param request 请求
     * @param <T>     响应类型
     * @return 请求
     */
    private <T extends BaseScrmResponse<?>> RequestEntity<?> createRequestEntity(BaseScrmRequest<T> request) {
        // 设置默认值
        if (StringUtils.isBlank(request.getDomain())) {
            request.setDomain(DOMAIN);
        }
        if (StringUtils.isBlank(request.getAccessId())) {
            request.setAccessId(ACCESS_ID);
        }
        if (StringUtils.isBlank(request.getAccessSecret())) {
            request.setAccessSecret(ACCESS_SECRET);
        }

        // 构建请求URL
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(request.getFullRequestUrl());

        // 生成签名，并添加到请求参数中
        request.generateSignature(uriBuilder);

        // GET 请求参数添加到 URL 中
        if (request.getRequestMethod() == HttpMethod.GET) {
            request.getRequestMap().forEach((key, value) -> uriBuilder.queryParamIfPresent(key, Optional.ofNullable(value)));
        }

        // POST 请求参数添加到 body，GET 请求无 body
        RequestEntity.BodyBuilder requestBuilder = RequestEntity.method(request.getRequestMethod(), uriBuilder.build().toUri());
        return request.getRequestMethod() == HttpMethod.GET ? requestBuilder.build()
                : requestBuilder.contentType(MediaType.APPLICATION_JSON).body(GsonUtil.objectToJson(request));
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 远程调用异常
     *
     * @param responseStr   响应结果
     * @param requestEntity 请求实体
     * @param e             异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, RequestEntity<?> requestEntity, Exception e) {
        String msg = "ScrmRpc异常 response: %s, %s url: %s".formatted(responseStr, requestEntity.getMethod(), requestEntity.getUrl());
        return SysException.of(SysErrorCode.S_RPC_ERROR, msg, e);
    }

    public static void main(String[] args) {
        ScrmRpc rpc = new ScrmRpc();
        Set<String> result = rpc.listPublicSecondAgentIds("221db78347194742aae29b8674a7f4b7");
        log.info("二级代理id: {}", String.join(",", result));
    }

}
