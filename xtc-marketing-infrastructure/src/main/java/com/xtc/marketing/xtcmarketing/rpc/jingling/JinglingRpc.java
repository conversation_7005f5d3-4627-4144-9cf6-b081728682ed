package com.xtc.marketing.xtcmarketing.rpc.jingling;

import com.xtc.marketing.xtcmarketing.exception.SysErrorCode;
import com.xtc.marketing.xtcmarketing.exception.SysException;
import com.xtc.marketing.xtcmarketing.rpc.jingling.request.BaseJinglingRequest;
import com.xtc.marketing.xtcmarketing.rpc.jingling.request.GetUserRequest;
import com.xtc.marketing.xtcmarketing.rpc.jingling.response.BaseJinglingResponse;
import com.xtc.marketing.xtcmarketing.rpc.jingling.response.GetUserResponse;
import com.xtc.marketing.xtcmarketing.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 精灵RPC
 */
@Slf4j
@Component
public class JinglingRpc {

    /**
     * 默认域名
     */
    private static final String DEFAULT_DOMAIN = "https://jl-gateway.okii.com";
    /**
     * 默认授权令牌
     */
    private static final String DEFAULT_AUTHORIZATION = "Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    /**
     * 默认应用id
     */
    private static final String DEFAULT_XTC_APP_ID = "channel-replenish";
    /**
     * http 请求实例
     */
    private static final RestTemplate REST_TEMPLATE = new RestTemplate();

    static {
        // 设置字符串的转换使用UTF-8编码
        REST_TEMPLATE.getMessageConverters().stream()
                .filter(converter -> converter.getClass().equals(StringHttpMessageConverter.class))
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));
        // 设置请求超时时间
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(3000);
        requestFactory.setReadTimeout(5000);
        REST_TEMPLATE.setRequestFactory(requestFactory);
    }

    /**
     * 查询用户的代理代码列表
     *
     * @param userId 用户id
     * @return 代理代码列表
     */
    public Set<String> listAgentCodes(String userId) {
        List<GetUserResponse.User> users = this.listUsers(userId);
        return users.stream()
                .map(GetUserResponse.User::getCusCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toUnmodifiableSet());
    }

    /**
     * 查询用户列表
     *
     * @param userId 用户id
     * @return 用户列表
     */
    public List<GetUserResponse.User> listUsers(String userId) {
        // 构建请求
        GetUserRequest request = new GetUserRequest();
        request.setPageSize(100);
        request.setPageIndex(1);

        // 设置查询条件
        GetUserRequest.QueryCondition condition = new GetUserRequest.QueryCondition();
        condition.setField("recvPerfWechatUserid");
        condition.setCompareType("Like");
        condition.setValue(userId);
        request.setAndQuery(List.of(condition));

        // 调用接口
        GetUserResponse response = this.call(request);
        return response.getData() != null ? response.getData() : List.of();
    }

    /**
     * 调用接口
     *
     * @param request 请求
     * @param <T>     响应类型
     * @return 响应
     */
    private <T extends BaseJinglingResponse> T call(BaseJinglingRequest<T> request) {
        // 初始化
        RequestEntity<?> requestEntity = this.createRequestEntity(request);
        String responseStr = "";
        String requestInfoLog = "精灵RPC %s url: %s, header: %s, body: %s"
                .formatted(requestEntity.getMethod(), requestEntity.getUrl(), requestEntity.getHeaders(), requestEntity.getBody());
        try {
            // 发起请求
            log.info(requestInfoLog);
            ResponseEntity<String> responseEntity = REST_TEMPLATE.exchange(requestEntity, String.class);
            responseStr = responseEntity.getBody();
            log.info("{}, response: {}", requestInfoLog, responseStr);
            // 解析响应
            T response = GsonUtil.jsonToBean(responseStr, request.getResponseClass());
            if (response == null || response.failure()) {
                throw this.remoteException();
            }
            return response;
        } catch (Exception e) {
            throw this.rpcSysException(responseStr, requestEntity, e);
        }
    }

    /**
     * 生成请求
     *
     * @param request 请求
     * @param <T>     响应类型
     * @return 请求
     */
    private <T extends BaseJinglingResponse> RequestEntity<?> createRequestEntity(BaseJinglingRequest<T> request) {
        // 设置默认值
        if (StringUtils.isBlank(request.getDomain())) {
            request.setDomain(DEFAULT_DOMAIN);
        }
        if (StringUtils.isBlank(request.getAuthorization())) {
            request.setAuthorization(DEFAULT_AUTHORIZATION);
        }
        if (StringUtils.isBlank(request.getXtcAppId())) {
            request.setXtcAppId(DEFAULT_XTC_APP_ID);
        }

        // 构建请求URL
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(request.getFullRequestUrl());

        // GET 请求参数添加到 URL 中
        if (request.getRequestMethod() == HttpMethod.GET) {
            String requestJson = GsonUtil.objectToJson(request);
            Map<String, Object> requestMap = GsonUtil.jsonToMap(requestJson);
            requestMap.forEach((key, value) -> uriBuilder.queryParamIfPresent(key, Optional.ofNullable(value)));
        }
        URI uri = uriBuilder.build().toUri();

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("authorization", request.getAuthorization());
        headers.set("xtc-app-id", request.getXtcAppId());

        // POST 请求参数添加到 body，GET 请求无 body
        RequestEntity.BodyBuilder requestBuilder = RequestEntity.method(request.getRequestMethod(), uri).headers(headers);
        return request.getRequestMethod() == HttpMethod.GET ? requestBuilder.build()
                : requestBuilder.contentType(MediaType.APPLICATION_JSON).body(GsonUtil.objectToJson(request));
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 远程调用异常
     *
     * @param responseStr   响应结果
     * @param requestEntity 请求实体
     * @param e             异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, RequestEntity<?> requestEntity, Exception e) {
        String msg = "精灵RPC异常 response: %s, %s url: %s".formatted(responseStr, requestEntity.getMethod(), requestEntity.getUrl());
        return SysException.of(SysErrorCode.S_RPC_ERROR, msg, e);
    }

    public static void main(String[] args) {
        JinglingRpc rpc = new JinglingRpc();
        Set<String> result = rpc.listAgentCodes("fdb8287fd5644571bff466ff1acfcf66");
        log.info("代理代码: {}", String.join(",", result));
    }

}
